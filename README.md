# Express.js Project

A modern Express.js API server with essential middleware and best practices.

## Features

- ✅ Express.js web framework
- ✅ CORS enabled
- ✅ Security headers with Helmet
- ✅ Request logging with Morgan
- ✅ Environment variables with dotenv
- ✅ JSON and URL-encoded body parsing
- ✅ Error handling middleware
- ✅ Development server with nodemon

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository or navigate to the project directory
2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a `.env` file (already created) and configure your environment variables

### Running the Application

#### Development Mode
```bash
npm run dev
```
This will start the server with nodemon for automatic restarts on file changes.

#### Production Mode
```bash
npm start
```

The server will start on `http://localhost:3000` (or the port specified in your `.env` file).

## API Endpoints

### Health Check
- **GET** `/api/health` - Returns server health status

### Users (Example endpoints)
- **GET** `/api/users` - Get all users
- **POST** `/api/users` - Create a new user
  - Body: `{ "name": "string", "email": "string" }`

### Root
- **GET** `/` - Welcome message with API info

## Project Structure

```
├── app.js              # Main application file
├── package.json        # Project dependencies and scripts
├── .env               # Environment variables
├── .gitignore         # Git ignore rules
└── README.md          # Project documentation
```

## Environment Variables

Configure these variables in your `.env` file:

- `PORT` - Server port (default: 3000)
- `NODE_ENV` - Environment (development/production)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test your changes
5. Submit a pull request

## License

ISC License
